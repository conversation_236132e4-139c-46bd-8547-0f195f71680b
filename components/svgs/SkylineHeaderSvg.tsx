import {StyleProps} from '../../lib';
import * as VFX from 'react-vfx';

export function SkylineHeaderSvg({className}: StyleProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 301.27 126.83"
      className={className}
    >
      <g style={{fill: '#fdf0da'}}>
        <g>
          <path d="M301.25,73.31c-.09-.55.24-3.99-.28-3.99h-4.27v-5.32s-.69.05-.76,0c-.04-.03.25-.39.11-.58-.18-.23-.68-.13-.88-.27-.59-.41.24-4.05,0-4.9,1.14-.64.97-3.79.72-4.94s-1.54-.91-1.67-1.09c-.07-.1.84-1.83-.66-1.92-1.77-.1-.75,2.02-.85,2.11-1.24-.13-1.46.15-1.66,1.29-.36,2.01.27,2.65.29,3.97,0,.26-.29.46-.34.74-.23,1.38.93,1.35,1.1,1.92.21.68-.12,1.82.21,2.6-.14.44-.86.24-.93.31-.19.19.46,1-.77.75v5.32h-.38v-6.93c-.91-.83-7.94-3.02-8.14-3.64-.09-.27-.09-1.72-.03-2.02.03-.15.3-.16.37-.27.48-.78-.05-.52-.18-.75-.55-.95.02-2.21,0-3.19-.01-.75-.52-1.3-.59-1.9-.1-.84.51-1.61.4-2.06-.04-.15-.4-.19-.54-.44-.58-.99.24-1.83.15-1.96-.08-.11-1.1-.21-.19-.48-.18-.19-.44-.34-.53-.61-.19-.6-.29-4.58-.52-4.61-.21-.03-.45.02-.66,0l-.41,4.63c-.11.34-1.21.39-.16.78-1.15.49-.07.65-.19,1.5-.08.59-.75,1.13-.76,1.2-.1.52.49,1.19.39,2.06-.07.6-.57,1.15-.59,1.9-.02.98.56,2.24,0,3.19-.13.23-.67-.03-.18.75.07.12.33.12.37.27.06.29.06,1.77-.03,2.02-.23.68-7.2,2.78-8.14,3.64v6.93h-1.33s-.19-17.28-.19-17.28h-22.03l-.57,17.28c-.14-.03-.76.04-.76,0v-10.26h-1.33s0,1.33,0,1.33h-3.42c.02-.17.16-.29.16-.47-.15-4.42-.08-9.22-.53-13.5-.09-.82.23-2.01-.58-2.55l-.38.76c-.09-1.15-.34-2.32-.66-3.42-.06-.21-.24-1.12-.57-.94-.55.29-.87,3.98-1.05,4.18-.35.39-.38-.72-.76-.38-.26.24-.16,1.72-.21,2.17-.34,3.25-.22,6.51-.56,9.89-.08.82.06,1.73,0,2.57-.56-.65-2.77-2.33-3.04-2.95-.17-.38.58-.14.35-.73-.25-.66-1.02-.66-1.28,0-.23.58.52.35.35.73-.08.19-2.75,2.86-2.94,2.95l-.57-.76-.28.38c-.19-.59-.77-4.41-.95-4.56-.02-.02-.54-.02-.56,0-.18.15-.85,3.98-.95,4.56l-.28-.38c-.23.2-.41.35-.47.67-.57,3.05.07,6.53-.2,9.68-.06.77-.09,1.24-.95,1.05v-26.31l-7.5-13.39-7.5,14.15v25.55h-1.33V30.01s.32-.03.47-.18c4.49-4.5,0-11.65-5.84-9.65-3.63,1.25-4.89,5.78-2.69,8.87.19.27,1.22,1.16,1.22,1.25v39.03h-1.71c.03-1.1-.37-1.51.95-1.33v-18.42h-2.66c-.06,0,.06-1.14,0-1.14h-7.41c-.14,0,.49-2-1.24-2.11-1.99-.12-1.46,2.11-1.61,2.11h-3.32c-.39,0-.05.87-.09,1.14-.23.24-2.78-.48-2.85.48v17.47c.03.74.86.35,1.14.48.06.03-.16.99,0,1.23l-1.52.1V26.97h-3.99c.2-.36-.29-1.63-.56-1.81-.28-.2-.89-.02-.96-.09-.06-.06.06-.44,0-.61s-.34-.14-.37-.28c-.29-1.19.06-3.83-.2-5.27-.05-.29-.77-1.36-.94-1.24,0,.46-.58.74-.67,1.11-.07.32.17.97.11,1.46-.05.44-.34.67-.4,1.12-.12.87.13,1.88-.04,2.81-.27,1.39-1.15.78-1.28.91-.07.07.07.64,0,.76-.02.03-.43-.27-.57.1l.19,1.04h-4.18v42.35h-.38v-37.32c0-.3-.62-1.24-1.05-1.23l-11.4-.02c-.61-.19-1.22,1.47-1.22,1.82v36.75h-.38V11.68c-.65-.34-1.91-.4-2.66-.09v-3.99c0-.07-1.33.07-1.33,0V3.42s-.95.05-.95,0V0h-13.67v3.42s-.95-.05-.95,0v4.18c0,.06-1.14-.06-1.14,0v30.2h-.19V11.4c-.44.14-2.66-.17-2.66.28v57.64h-.38V29.53c0-.32-.9-.25-.94-.29-.2-.19.22-2.25.08-2.47-.12-.18-.49-.26-.66-.47-.81-1.04-1.24-1.94-2.49-2.64-.46-.26-2.29-.94-2.73-.88-.2.03-.07.4-.1.57-.32.05-.19-.47-.48-.55-.76-.2-3.16,1.1-3.79,1.69s-.99,1.57-1.41,1.97c-.2.19-.5.09-.55.14-.18.19.11,2.65-.03,2.83-.03.03-.76-.37-.76.09v39.79h-.57V23.08c0-.89-.52-.78-.57-.99-.12-.44.13-2.59,0-2.72-.36-.06-2.2.08-2.28,0-.05-.05.08-.93-.1-1.33-.38-.85-3.28-3.05-3.78-4.01-.34-.64-.31-1.56-.3-2.27h-.57s-.37,2.39-.37,2.39c-.57.83-3.44,3.22-3.72,3.89-.1.24.05,1.01.1,1.32h-2.47c-.26.27.51,2.92-.48,3.04l-.09,46.8c-.04.33-.76.09-.76-.36v-10.92h-3.8v-3.42h-5.32v-4.56h-9.12s.04.8,0,.95c-.68.16-.29-.75-.38-1.14h2.28s0-5.13,0-5.13h-6.27v20.13h-.76v-22.41h-2.09c-.11-.61.36-2.47-.47-2.47h-8.17s-.28.26-.28.28v13.77h-4.75v12.16h-.19v-7.22h-8.07s-.28.26-.28.28v4.65h-5.51v5.51h-1.33v-6.74c0-.07.24-.19.11-.47-.11-.23-1.05-1.09-1.35-1.15h-11.35c-.86-.37-.63-1.77-.73-1.88-.04-.04-.49-.08-.75-.29-.39-.32-.78-1.09-1.31-1.54-1.08-.92-1.96-.86-2.91-1.41-.16-.1-.19-.4-.32-.48-.26-.16-2.04-.18-2.35-.07-.28.1-.3.48-.51.63-.57.39-1.68.43-2.63,1.17-.76.59-1.02,1.4-1.39,1.74-.23.22-.82.17-.89.25-.12.14.06.87-.02,1.17-.03.1-.6.48-.71.71H5.06c-.3.06-1.24.92-1.35,1.15-.13.27.11.4.11.47v6.74H.31c-.53,0-.19,3.43-.28,3.99h.57v-3.42h3.8v-7.69c0-.07.48-.46.62-.52,3.95-.32,8-.07,11.99-.13.07-.41.57-.85.65-1.1.09-.3-.04-.86.07-.98.05-.05.41-.03.63-.22.27-.23.55-.94.95-1.34,1.14-1.11,2.18-.97,3.21-1.55.29-.16.2-.5.59-.55.26-.03,1.08-.04,1.3.01.17.04.2.4.45.54.77.43,1.72.37,2.71,1.1.8.58,1.03,1.4,1.44,1.79.2.19.56.15.63.22.08.09.35,2.1.87,2.2.1.02.5-.14.76-.15,3.15-.04,6.8-.2,9.9,0,.37.02,1.79.15,1.79.67v7.69h2.47v-5.51h6.46v2.28s1.33,0,1.33,0v-3.42h-2.28v-3.8h6.93s.28.26.28.28v6.93h1.33v-12.16h4.75v-14.05h7.79v2.47s2.09,0,2.09,0v22.41h2.09v-20.13h4.94v3.99c-2.36-.17-2.44-.08-2.28,2.28,1.31-.03,1.72.42,1.52-1.14h7.98v4.75h5.32v3.42h3.8l.2,11.39h1.7s0-45.48,0-45.48c.27-1.55.81-2.81.58-4.46.38-.06,2.38.09,2.47,0,.08-.08-.12-1.41.1-1.9.34-.75,3.01-2.89,3.61-3.8l3.8,3.99c.24.32,0,1.61.1,1.71.08.08,1.92-.06,2.28,0,.17,1-.48,2.46.58,2.98v46.97s1.7,0,1.7,0V29.82c.18-.03.71.05.76,0,.09-.09-.1-2.04.04-2.53.06-.19,1.35-1.99,1.57-2.23.62-.64,2.15-1.57,3.05-1.54.36.01.19.39.56.42.76.06,1.04-.45,1.66-.37,1.34.16,3.68,2.33,4.05,3.57.18.59-.01,2.58.09,2.67.06.06.74-.04.95,0v40.08h1.52V12.16c0-.08,1.29-.09,1.52,0v25.93c0,.34,1.06.32,1.33.28V8.17c0-.06,1.14.06,1.14,0V3.99s.89.06.95,0c.21-.21-.11-2.93,0-3.42h12.54v3.42s.95-.05.95,0v3.7s-.15.11-.15.19c.04.44,1.14.26,1.48.28l-.17,29.9c.11.43.95.29,1.31.3V12.25c0-.26,1.52-.18,1.52-.09v57.74h1.52v-37.32c0-.26.55-1.44,1.03-1.25l11.12.11c.09.05.38.52.38.57v37.89c1.82.17,1.66-.09,1.71-1.6.44-12.72.4-26.99,0-39.72-.01-.37-.15-.68-.2-1.03h3.89c.27,0,.46-1.67.69-1.87.17-.15.85.04.92-.03.05-.05-.06-.59,0-.82.04-.15.34-.12.37-.26.18-.74-.12-2.01,0-2.82.06-.42.34-.66.38-.95.09-.7-.04-1.47.01-2.17.34.07.35.37.39.66.21,1.92-.07,3.91.55,5.73.06.19-.03.59.01.64.07.08,1.07-.08,1.14,0,.05.06-.06.44,0,.61s.34.14.37.28c.06.24-.05,1.01,0,1.01h3.99v42.35h2.66c-.07-.38.09-2.38,0-2.47-.06-.06-.74.04-.95,0v-17.28c1.31-.14,3.03.67,2.66-1.33.51-.03,3.3.12,3.42,0,.16-.16-.47-1.92.85-1.92s.57,1.72,1.04,2.02l7.22-.11c.07.07-.07,1.33,0,1.33h2.66v17.28c-.21.04-.89-.06-.95,0-.09.09.07,2.09,0,2.47h2.85V30.1c0-.13-1.42-1.25-1.52-1.61.74-.1,1.01.69,1.53,1.03,1.44.96,3.04,1.17,4.68.52.94-.37,1.45-1.19,2.34-1.56.16.55-1.33,1.3-1.33,1.42v39.98h2.47v-25.93c2.25-4.47,4.77-8.83,6.84-13.39l7.03,12.63v26.69c1.9.17,1.96-.02,2.1-1.8.19-2.36-.46-6.68.18-8.7.06-.2.39-.19.53-.46.39-.79.49-2.56.8-3.48.29-.05.22.35.28.57.21.82.39,2.44.66,3.1.09.21.38.08.51.18.16.13.21.48.35.52.19.05,2.32-2.4,2.75-2.76.27-.22.57-.42.86-.62,1.07.8,2.02,1.75,2.95,2.71.2.21.82,1.35,1.11.36.08-.26-.25-2.3-.18-3.02.3-3.26.11-6.39.39-9.49.14-1.58.61-1.07.77-1.36.39-.7.54-2.8.75-3.68.85.68.61,2.98,1.12,3.69.14.2.52,0,.6.23.62,1.81.03,6.66.18,8.9s.21,3.86.18,6.26c-.01,1.05-.08,2.14.2,3.14.62.19.22-.52.48-.58.99-.24,2.96.2,4.08.01v6.27c.62-.09,1.76.33,1.9-.47l.66-16.81h20.8l.19,17.28h2.47v-7.12l8.07-3.51c.13-1.87.04-3.74.1-5.61.04-1.34.27-1.85.38-3.02.04-.45.19-1.58-.39-1.44-.08-.29.23-.09.37-.33.6-1,.24-2,.4-2.98.05-.29.31-.49.36-.78.07-.41-.2-1.24.2-1.41.52,1.44.28,3.92.96,5.17.11.2.35.13.37.14.2.17-.36.09-.39.46-.12,1.1.14,2.07.52,3.08l-.1,6.59,8.04,3.54c-.15,2.37.42,4.87-.09,7.22h1.71s0-5.32,0-5.32c1.23.21.55-.44.79-.73.03-.04.71-.36.83-.41.14-.06.03.33.22.38.38.1.98-.07,1.4,0-.37-.91-.13-1.96-.19-2.94-.03-.53-.35-1.3-.36-1.73,0-.05.68-.11.36-.46-.14-.16-1.22-.22-1.52-.19.21-.88-.47-2.01-.48-2.75-.01-.69.55-1.5.1-2.38l-.57.19.1-.57c1.56-.22.81,1.27,1.8,1.9l.38-2.09c1.32.04,1.54.66,1.23,1.9-.2,0,.06-1.05-.38-1.14-.78-.15-.28,1.29-.27,1.61.02,1.21.01,2.42,0,3.61s-.59,4.19.17,4.91c.2.19.52.14.66.31.07.09-.08.37.1.48.2.12.58.07.58.1v5.32h4.56v3.42h.57ZM199.89,22.1c1.72-1.86,4.87-2.18,6.95-.72.23.16,1.71,1.65,1.53,1.79-.4.31-1.19-.96-1.52-1.23-2.07-1.73-4.45-1.49-6.44.21-.34.29-.67,1.24-1.16.93.27-.28.37-.7.63-.98ZM200.02,23.74c1.5-3.18,6.07-3.09,7.6,0h-1.9c.16-.34.24-.66.21-1.04-.09-1.02-.57-.48-1.24-.46-.45,0-2.8-.15-2.94,0l.17,1.5h-1.9ZM198.69,24.31h10.07c.38,1.03.13,2,0,3.04h-9.88l-.19-3.04ZM244.46,60.97v-1.33s.19,0,.19,0v3.42h-4.18v-2.09h3.99Z" />
          <path d="M205.9,30.39c-.62.3-1.99.1-1.99.85.49,12.71-.29,25.49,0,38.19.02.89,1.32.33,1.99.47V30.39ZM204.57,69.32V31.43c0-.2.54-.36.76-.28v38.18s-.73.03-.76,0Z" />
          <path d="M201.73,30.39v39.51h1.9V30.77c0-.1-1.6-.1-1.9-.38ZM202.3,31.15c.13,0,.76.15.76.19v37.99h-.95c-.09-.07.19-.24.19-.28V31.15Z" />
          <path d="M27.94,62.68h-1.14c-.07.83-.42,6.23-.19,6.46.06.06,1.05-.07,1.39.03.43.13.41.65.64.73,1.24.41.3-1.16.24-1.79-.2-1.85-.14-4.78.58-6.47-.06-.3-5.03-1.65-5.76-1.95l-5.82,1.85c-.2.46.32.3.38.51.46,1.58.26,5.47-.13,7.05-.35,1.38.91.96,1.08-.06.33-2.01-.11-5.02-.16-7.09l4.67-1.49,4.51,1.45.13.3-.4,3.71v-3.23Z" />
          <path d="M222.26,44.09l-12.46,23.54-.11,2.08c1.82.18,1.75-.04,2.44-1.45,3.76-7.68,6.8-15.91,10.58-23.61.18-.57.17-.67-.46-.55ZM210.27,69.13l.15-1.47,6.97-13.16.48-.57-6.67,15.17-.93.02Z" />
          <path d="M125.37,32.67c-2.43-.63-1.16,1.93-1.52,2.28-.07.07-1.41-.05-1.71,0v2.85h-2.66v-2.85c-.3-.05-1.64.07-1.71,0-.08-.08.06-1.92,0-2.28h-1.33v3.42c0,.09,1.71-.09,1.71,0v3.23h5.32v-3.23c0-.09,1.9.09,1.9,0v-3.42Z" />
          <path d="M283.97,63.34c.04-.13.67-.54.57-.66-1.1-.04-3.58-1.77-4.45-1.71-.97.06-3.1,1.65-4.29,1.71-.07.09.57.69.57.85v6.36h.57v-6.74c.72-.3,2.63-1.4,3.29-1.37.15,0,2.65.98,2.82,1.09.13.08.34.36.34.47v6.55h.57v-6.55Z" />
          <path d="M22.62,62.96c-.12-.39-.97-.29-1.33-.29.07,2.17-.05,4.31-.19,6.46h1.52v-6.17Z" />
          <path d="M26.23,62.96c-.12-.39-.97-.29-1.33-.29.05,2.16,0,4.32-.19,6.46h1.52v-6.17Z" />
          <polygon points="23.19 62.68 23 69.14 24.52 69.14 24.33 62.68 23.19 62.68" />
          <path d="M19.39,69.13c2.1.14,1.36-.29,1.32-1.61-.02-.75.17-4.53-.17-4.77-.28-.2-1.15-.08-1.15.2v6.17Z" />
          <path d="M119.49,29.63v4.94h2.85v-4.94h-2.85ZM120.06,30.2h1.71v3.8h-1.71v-3.8Z" />
        </g>
        <text
          transform="translate(70.71 114.42)"
          style={{
            fontFamily: 'var(--font-league-gothic), \'League Gothic\', Arial, sans-serif',
            fontSize: '49.64px',
            fontVariationSettings: '\'wdth\' 100',
          }}
        >
          <tspan x="0" y="0">
            Sean &amp; Eva
          </tspan>
        </text>
      </g>
    </svg>
  );
}
